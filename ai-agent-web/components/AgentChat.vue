<template>
  <div class="agent-chat">
    <div class="chat-header">
      <button @click="$emit('back')" class="back-btn">←</button>
      <h3 class="agent-name">{{ agent.name }}</h3>
      <span v-if="!iframeLoaded" class="loading-text">Loading...</span>
    </div>

    <div class="chat-content">
      <!-- Error display -->
      <div v-if="iframeError" class="error-container">
        <p class="error-message">{{ iframeError }}</p>
        <p class="error-url">URL: {{ agent.difyUrl }}</p>
        <button @click="retryLoad" class="retry-button">Retry</button>
      </div>

      <!-- Iframe -->
      <iframe
        :key="`iframe-${agent.id}-${Date.now()}`"
        :src="agent.difyUrl"
        class="dify-iframe"
        allow="microphone; camera; geolocation; autoplay; encrypted-media; fullscreen"
        referrerpolicy="no-referrer-when-downgrade"
        @load="handleIframeLoad"
        @error="handleIframeError"
        :title="`${agent.name} Chat Interface`"
        loading="eager"
      ></iframe>

      <!-- Loading overlay -->
      <div v-if="!iframeLoaded && !iframeError" class="loading-overlay">
        <div class="loading-content">
          <div class="loading-title">🤖 正在启动AI助手</div>
          <div class="loading-description">正在加载Dify应用，请稍候...</div>
          <div class="loading-spinner"></div>
          <div class="loading-note">首次加载可能需要10-15秒</div>
        </div>
      </div>

      <!-- Debug info -->
      <div class="debug-status">
        Status: {{ iframeLoaded ? 'Loaded' : iframeError ? 'Error' : 'Loading' }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted, watch } from 'vue'

export default {
  name: 'AgentChat',
  props: {
    agent: {
      type: Object,
      required: true
    }
  },
  emits: ['back'],
  setup(props) {
    const iframeLoaded = ref(false)
    const iframeError = ref(null)
    let checkTimer = null
    let timeoutTimer = null

    const handleIframeLoad = () => {
      console.log('Iframe loaded successfully:', props.agent.difyUrl)
      iframeLoaded.value = true
      iframeError.value = null
    }

    const handleIframeError = (error) => {
      console.error('Iframe load error:', error, 'URL:', props.agent.difyUrl)
      iframeError.value = 'Failed to load Dify application'
    }

    const retryLoad = () => {
      iframeError.value = null
      iframeLoaded.value = false
    }

    const checkIframeContent = () => {
      try {
        const iframe = document.querySelector(`iframe[src="${props.agent.difyUrl}"]`)
        if (iframe && iframe.contentDocument) {
          const doc = iframe.contentDocument
          const loadingElement = doc.querySelector('.spin-animation')
          const chatElement = doc.querySelector('[data-testid="chat"], .chat-container, [class*="chat"]')

          if (!loadingElement && chatElement) {
            console.log('Dify app fully loaded - chat interface detected')
            iframeLoaded.value = true
            iframeError.value = null
            return true
          }
        }
      } catch (e) {
        // 跨域限制，无法访问iframe内容，依赖onLoad事件
        console.log('Cannot access iframe content due to CORS, relying on onLoad event')
      }
      return false
    }

    const startChecking = () => {
      // 定期检查iframe内容
      if (!iframeLoaded.value && !iframeError.value) {
        checkTimer = setInterval(checkIframeContent, 2000)
      }

      // 超时处理
      timeoutTimer = setTimeout(() => {
        if (!iframeLoaded.value && !iframeError.value) {
          console.warn('Iframe taking too long to load:', props.agent.difyUrl)
          // 不设置错误，让用户继续等待
        }
      }, 15000) // 15秒超时
    }

    const stopChecking = () => {
      if (checkTimer) {
        clearInterval(checkTimer)
        checkTimer = null
      }
      if (timeoutTimer) {
        clearTimeout(timeoutTimer)
        timeoutTimer = null
      }
    }

    // 监听 agent 变化，重置状态
    watch(() => props.agent, () => {
      iframeLoaded.value = false
      iframeError.value = null
      stopChecking()
      startChecking()
    }, { immediate: true })

    onMounted(() => {
      startChecking()
    })

    onUnmounted(() => {
      stopChecking()
    })

    return {
      iframeLoaded,
      iframeError,
      handleIframeLoad,
      handleIframeError,
      retryLoad
    }
  }
}
</script>

<style scoped>
.agent-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-header {
  padding: 12px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 12px;
}

.back-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
}

.agent-name {
  margin: 0;
  color: #1f2937;
  flex: 1;
}

.loading-text {
  font-size: 12px;
  color: #6b7280;
}

.chat-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  height: 100%;
  background-color: #f9fafb;
}

.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #ef4444;
  padding: 20px;
}

.error-message {
  margin-bottom: 10px;
}

.error-url {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 15px;
}

.retry-button {
  padding: 8px 16px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.dify-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 0;
  background-color: white;
  z-index: 1;
}

.loading-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #6b7280;
  z-index: 2;
  background: rgba(255,255,255,0.95);
  padding: 30px;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  max-width: 300px;
}

.loading-title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 500;
}

.loading-description {
  margin-bottom: 15px;
  font-size: 14px;
  color: #374151;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  margin: 0 auto 15px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-note {
  font-size: 12px;
  color: #9ca3af;
}

.debug-status {
  position: absolute;
  bottom: 10px;
  right: 10px;
  font-size: 10px;
  color: #9ca3af;
  background: rgba(255,255,255,0.8);
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 3;
}
</style>