<template>
  <div class="agent-portal">
    <style>
      .loading-spinner {
        animation: spin 1s linear infinite;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>

    <div class="portal-header">
      <h2>AI Agent 助手</h2>
      <button @click="handleClose" class="close-btn">×</button>
    </div>

    <div class="portal-content">
      <AgentChat
        :agent="agent"
        @back="goBack"
      />

      <!-- Debug info -->
      <div class="debug-info">
        Selected Agent: {{ selectedAgent ? selectedAgent.name : 'None' }}
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import AgentChat from './AgentChat.vue'

export default {
  name: 'AgentPortal',
  components: {
    AgentChat
  },
  setup() {
    const mounted = ref(false)
    const selectedAgent = ref(null)
    const config = ref(null)

    const agents = [
      {
        id: 'customer-service',
        name: '客服助手',
        description: '专业的客户服务支持',
        difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
      },
      {
        id: 'sales-assistant',
        name: '销售顾问',
        description: '产品咨询和销售支持',
        difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
      }
    ]

    const agent = {
      id: 'customer-service',
      name: '客服助手',
      description: '专业的客户服务支持',
      difyUrl: 'http://************:9009/chatbot/05VnYqM6Gz8fbnrl'
    }

    const handleMessage = (event) => {
      console.log('Portal received message:', event.data)
      if (event.data.type === 'ai-agent-config') {
        console.log('Received config:', event.data.config)
        config.value = event.data.config
      }
    }

    const handleError = (event) => {
      console.error('Portal global error:', event.error)
    }

    const handleClose = () => {
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'ai-agent-close'
        }, '*')
      }
    }

    const selectAgent = (agent) => {
      console.log('Selecting agent:', agent)
      selectedAgent.value = agent
    }

    const goBack = () => {
      selectedAgent.value = null
    }

    onMounted(() => {
      mounted.value = true

      // 监听来自父窗口的消息
      window.addEventListener('message', handleMessage)

      // 通知父窗口iframe已准备就绪
      if (window.parent !== window) {
        console.log('Portal sending ready message to parent')
        window.parent.postMessage({
          type: 'ai-agent-ready'
        }, '*')
      }

      // 添加全局错误监听
      window.addEventListener('error', handleError)
    })

    onUnmounted(() => {
      window.removeEventListener('message', handleMessage)
      window.removeEventListener('error', handleError)
    })

    return {
      mounted,
      selectedAgent,
      config,
      agents,
      agent,
      handleClose,
      selectAgent,
      goBack
    }
  }
}
</script>

<style scoped>
.agent-portal {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, sans-serif;
  border-left: 1px solid #e5e7eb;
}

.portal-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.portal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 0;
  width: 24px;
  height: 24px;
}

.portal-content {
  flex: 1;
  position: relative;
}

.debug-info {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 5px;
  font-size: 12px;
  border-radius: 3px;
  z-index: 1000;
}
</style>