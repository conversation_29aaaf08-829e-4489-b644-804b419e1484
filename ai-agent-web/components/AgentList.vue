<template>
  <div class="agent-list">
    <div class="list-header">
      <h3>选择AI助手</h3>
    </div>
    
    <div class="agents">
      <div 
        v-for="agent in agents" 
        :key="agent.id"
        class="agent-card"
        @click="$emit('select', agent)"
      >
        <div class="agent-icon">🤖</div>
        <div class="agent-info">
          <h4>{{ agent.name }}</h4>
          <p>{{ agent.description }}</p>
        </div>
        <div class="agent-arrow">→</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AgentList',
  props: {
    agents: {
      type: Array,
      required: true
    }
  },
  emits: ['select']
}
</script>

<style scoped>
.agent-list {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.list-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #374151;
}

.agent-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.agent-card:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

.agent-icon {
  font-size: 24px;
  margin-right: 12px;
}

.agent-info {
  flex: 1;
}

.agent-info h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  color: #1f2937;
}

.agent-info p {
  margin: 0;
  font-size: 12px;
  color: #6b7280;
}

.agent-arrow {
  color: #9ca3af;
  font-size: 16px;
}
</style>