import { _class_apply_descriptor_get } from "./_class_apply_descriptor_get.js";
import { _class_extract_field_descriptor } from "./_class_extract_field_descriptor.js";

export function _class_private_field_get(receiver, privateMap) {
    var descriptor = _class_extract_field_descriptor(receiver, privateMap, "get");
    return _class_apply_descriptor_get(receiver, descriptor);
}
export { _class_private_field_get as _ };
