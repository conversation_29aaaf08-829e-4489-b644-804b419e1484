"use strict";

var _class_apply_descriptor_get = require("./_class_apply_descriptor_get.cjs");
var _class_check_private_static_access = require("./_class_check_private_static_access.cjs");
var _class_check_private_static_field_descriptor = require("./_class_check_private_static_field_descriptor.cjs");

exports._ = exports._class_static_private_field_spec_get = _class_static_private_field_spec_get;
function _class_static_private_field_spec_get(receiver, classConstructor, descriptor) {
    _class_check_private_static_access._(receiver, classConstructor);
    _class_check_private_static_field_descriptor._(descriptor, "get");

    return _class_apply_descriptor_get._(receiver, descriptor);
}
